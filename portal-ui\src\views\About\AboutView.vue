<template>
  <Layout>
    <!-- 顶部Banner -->
    <div class="about-banner">
      <div
        class="about-banner-bg"
        style="background: url('/images/back3.webp') center/cover; opacity: 0.2; z-index: 1; position: absolute; top: 0; left: 0; right: 0; bottom: 0;"
      ></div>
      <div class="banner-content">
        <h1 class="banner-title">重塑算力想象，让智能无处不在</h1>
        <p class="banner-subtitle">我们相信</p>
        <p class="banner-subtitle">人类无需再围成一台机器</p>
        <p class="banner-subtitle">而是用智能连接彼此，释放算力的真正价值</p>
      </div>
    </div>

    <!-- 关于我们 -->
    <section class="about-section">
      <div class="container">
        <div class="am-g">
          <div class="am-u-md-6">
            <div class="our-company-text">
              <h3>关于我们</h3>
              <p>
                天工开物智能科技（苏州）有限公司，致力于打造面向企业级用户的高性能计算解决方案，
                围绕"高效调度、低门槛使用、专业保障"的核心理念，为 AI、大模型、图形渲染、科研计算等场景提供灵活、稳定、弹性的算力支持。
              </p>
              <p>
                我们基于全国分布式算力网络，自主构建智算调度平台，整合GPU资源与数据中心节点，
                为企业提供从算力资源租用、模型部署优化到全流程运维服务的一站式专业方案。
              </p>
              <p>
                在智能时代的浪潮中，天工开物致力于打造企业级专业算力服务平台，
                以全国分布式高性能计算网络为基础，提供稳定、高效、灵活可控的算力解决方案。
              </p>
            </div>
          </div>
          <div class="am-u-md-6">
            <div class="our-company-quote">
              <div class="our-company-img">
                <img src="/images/tiangonghead.jpeg" alt="天工开物智能科技">
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 数据统计 -->
    <!-- <section class="stats-section">
      <div class="container">
        <div class="am-g">
          <div class="am-u-sm-6 am-u-md-3">
            <div class="stat-item">
              <div class="stat-number">32%</div>
              <div class="stat-label">市场占有率</div>
            </div>
          </div>
          <div class="am-u-sm-6 am-u-md-3">
            <div class="stat-item">
              <div class="stat-number">27%</div>
              <div class="stat-label">年增长率</div>
            </div>
          </div>
          <div class="am-u-sm-6 am-u-md-3">
            <div class="stat-item">
              <div class="stat-number">3000+</div>
              <div class="stat-label">服务客户</div>
            </div>
          </div>
          <div class="am-u-sm-6 am-u-md-3">
            <div class="stat-item">
              <div class="stat-number">10000+</div>
              <div class="stat-label">GPU节点</div>
            </div>
          </div>
        </div>
      </div>
    </section> -->

    <!-- 选择我们的理由 -->
    <section class="our-mission">
      <div class="container">
        <div class="section--header">
          <h2 class="section--title">选择我们的理由</h2>
        </div>
        <div class="am-g">
          <div class="am-u-sm-12 am-u-md-6 am-u-lg-3">
            <div class="our_mission--item">
              <div class="our_mission--item_media">
                <i class="am-icon-server" style="font-size: 48px; color: #1470FF;"></i>
              </div>
              <h4 class="our_mission--item_title">企业级专业服务</h4>
              <div class="our_mission--item_body">
                <p>为AI、科研、图形渲染、工业仿真等场景，提供稳定高效的高性能计算支持</p>
              </div>
            </div>
          </div>
          <div class="am-u-sm-12 am-u-md-6 am-u-lg-3">
            <div class="our_mission--item">
              <div class="our_mission--item_media">
                <i class="am-icon-globe" style="font-size: 48px; color: #1470FF;"></i>
              </div>
              <h4 class="our_mission--item_title">全国分布式节点布局</h4>
              <div class="our_mission--item_body">
                <p>多地部署，动态调度，资源灵活，负载均衡，响应迅速</p>
              </div>
            </div>
          </div>
          <div class="am-u-sm-12 am-u-md-6 am-u-lg-3">
            <div class="our_mission--item">
              <div class="our_mission--item_media">
                <i class="am-icon-cogs" style="font-size: 48px; color: #1470FF;"></i>
              </div>
              <h4 class="our_mission--item_title">灵活弹性 + 高性价比</h4>
              <div class="our_mission--item_body">
                <p>自研调度平台，支持定制、按需计费与大客户深度合作</p>
              </div>
            </div>
          </div>
          <div class="am-u-sm-12 am-u-md-6 am-u-lg-3">
            <div class="our_mission--item">
              <div class="our_mission--item_media">
                <i class="am-icon-users" style="font-size: 48px; color: #1470FF;"></i>
              </div>
              <h4 class="our_mission--item_title">更懂企业的算力伙伴</h4>
              <div class="our_mission--item_body">
                <p>从需求对接、技术支持到运维保障，全流程一对一服务</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心团队 -->
    <section class="our-team">
      <div class="container">
        <div class="section--header">
          <h2 class="section--title">核心团队</h2>
          <p class="section--description">
            核心团队来自知名AI云计算厂商、IDC运维专家与高校科研机构
          </p>
        </div>
        <div class="am-g">
          <div class="am-u-sm-12 am-u-md-4">
            <div class="team-box">
              <div class="our-team-img">
                <img src="/images/back1.webp" alt="技术团队">
              </div>
              <div class="team_member--body">
                <h4 class="team_member--name">技术研发</h4>
                <span class="team_member--position">专业的研发团队，深耕AI算力调度与优化</span>
              </div>
            </div>
          </div>
          <div class="am-u-sm-12 am-u-md-4">
            <div class="team-box">
              <div class="our-team-img">
                <img src="/images/back1.webp" alt="运维团队">
              </div>
              <div class="team_member--body">
                <h4 class="team_member--name">运维保障</h4>
                <span class="team_member--position">5x8小时专业运维，确保服务稳定可靠</span>
              </div>
            </div>
          </div>
          <div class="am-u-sm-12 am-u-md-4">
            <div class="team-box">
              <div class="our-team-img">
                <img src="/images/back1.webp" alt="客服团队">
              </div>
              <div class="team_member--body">
                <h4 class="team_member--name">客户服务</h4>
                <span class="team_member--position">专业客户经理，提供一对一贴心服务</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 联系我们 -->
    <section class="contact-section">
      <div class="container">
        <div class="am-g">
          <div class="am-u-md-4">
            <div class="contact-item">
              <i class="am-icon-phone"></i>
              <h4>联系电话</h4>
              <p>400-XXX-XXXX</p>
            </div>
          </div>
          <div class="am-u-md-4">
            <div class="contact-item">
              <i class="am-icon-envelope"></i>
              <h4>邮箱地址</h4>
              <p><EMAIL></p>
            </div>
          </div>
          <div class="am-u-md-4">
            <div class="contact-item">
              <i class="am-icon-map-marker"></i>
              <h4>公司地址</h4>
              <p>江苏省苏州市</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 立即开始 -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content">
          <h2>立即开启智算之旅</h2>
          <p>连接智算未来，让高性能计算像水电一样可得、可控、可负担</p>
          <div class="cta-buttons">
            <button class="am-btn am-btn-primary am-btn-lg" @click="startTrial">立即开始</button>
          </div>
        </div>
      </div>
    </section>
  </Layout>
</template>

<script>
import Layout from "@/components/common/Layout";

export default {
  name: 'AboutView',
  components: { Layout },
  methods: {
    startTrial() {
      this.$router.push('/product');
    },
    contactUs() {
      // 可以添加联系我们的逻辑
      console.log('联系我们');
    }
  }
}
</script>

<style scoped>
/* 顶部Banner */
.about-banner {
  background: linear-gradient(135deg, #1470FF 0%, #4A90FF 100%);
  color: white;
  padding: 200px 0 200px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.about-banner-bg {
  pointer-events: none; /* 确保背景层不影响点击 */
}


.banner-content {
  position: relative;
  z-index: 2;
}

.banner-title {
  font-size: 48px;
  font-weight: 600;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.banner-subtitle {
  font-size: 20px;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

/* 关于我们部分 */
.about-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e6f2ff 100%);
  position: relative;
}

.about-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23d4e8ff" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  z-index: 1;
}

.about-section .container {
  position: relative;
  z-index: 2;
}

.container {
  width: 95%;
  max-width: 1170px;
  margin: 0 auto;
  padding: 0 15px;
}

/* 数据统计
.stats-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e6f2ff 100%);
  padding: 60px 0;
  position: relative;
}

.stats-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23d4e8ff" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
  z-index: 1;
}

.stats-section .container {
  position: relative;
  z-index: 2;
}

.stat-item {
  text-align: center;
  padding: 30px 15px;
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

.stat-number {
  font-size: 48px;
  font-weight: 700;
  color: #1470FF;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(20, 112, 255, 0.1);
}

.stat-label {
  font-size: 16px;
  color: #555;
  font-weight: 500;
} */

/* 选择我们的理由 */
.our-mission {
  padding: 80px 0;
  background: #fff;
  position: relative;
}

.our-mission::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 49%, rgba(20, 112, 255, 0.02) 50%, transparent 51%);
  z-index: 1;
}

.our-mission .container {
  position: relative;
  z-index: 2;
}

.section--header {
  text-align: center;
  margin-bottom: 60px;
}

.section--title {
  font-size: 36px;
  color: #333;
  margin-bottom: 20px;
  font-weight: 600;
  position: relative;
}

.section--title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #1470FF, #4A90FF);
  border-radius: 2px;
}

.section--description {
  font-size: 18px;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.our_mission--item {
  text-align: center;
  padding: 40px 20px;
  transition: all 0.3s ease;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
}

.our_mission--item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(20, 112, 255, 0.05) 0%, rgba(74, 144, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.our_mission--item:hover::before {
  opacity: 1;
}

.our_mission--item:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(20, 112, 255, 0.15);
}

.our_mission--item_media {
  margin-bottom: 25px;
  position: relative;
  z-index: 2;
}

.our_mission--item_media i {
  transition: all 0.3s ease;
}

.our_mission--item:hover .our_mission--item_media i {
  color: #1470FF !important;
  transform: scale(1.1);
}

.our_mission--item_title {
  font-size: 20px;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.our_mission--item_body {
  position: relative;
  z-index: 2;
}

.our_mission--item_body p {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 核心团队 */
.our-team {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e6f2ff 100%);
  position: relative;
}

.our-team::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%23d4e8ff" opacity="0.5"/><circle cx="80" cy="20" r="2" fill="%23d4e8ff" opacity="0.5"/><circle cx="20" cy="80" r="2" fill="%23d4e8ff" opacity="0.5"/><circle cx="80" cy="80" r="2" fill="%23d4e8ff" opacity="0.5"/></svg>');
  z-index: 1;
}

.our-team .container {
  position: relative;
  z-index: 2;
}

.team-box {
  background: #fff;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(20, 112, 255, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 30px;
  border: 1px solid rgba(20, 112, 255, 0.1);
}

.team-box:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(20, 112, 255, 0.2);
  border-color: rgba(20, 112, 255, 0.3);
}

.our-team-img {
  position: relative;
  overflow: hidden;
}

.our-team-img::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(20, 112, 255, 0.1) 0%, rgba(74, 144, 255, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.team-box:hover .our-team-img::after {
  opacity: 1;
}

.our-team-img img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.team-box:hover .our-team-img img {
  transform: scale(1.05);
}

.team_member--body {
  padding: 30px 25px;
  text-align: center;
  position: relative;
}

.team_member--name {
  font-size: 20px;
  color: #333;
  margin-bottom: 10px;
  font-weight: 600;
}

.team_member--position {
  font-size: 15px;
  color: #666;
  line-height: 1.5;
}

/* 联系我们 */
.contact-section {
  padding: 80px 0;
  background: #fff;
  position: relative;
}

.contact-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 49%, rgba(20, 112, 255, 0.02) 50%, transparent 51%);
  z-index: 1;
}

.contact-section .container {
  position: relative;
  z-index: 2;
}

.contact-item {
  text-align: center;
  padding: 40px 20px;
  transition: all 0.3s ease;
  border-radius: 10px;
  position: relative;
}

.contact-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(20, 112, 255, 0.05) 0%, rgba(74, 144, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 10px;
}

.contact-item:hover::before {
  opacity: 1;
}

.contact-item:hover {
  transform: translateY(-5px);
}

.contact-item i {
  font-size: 48px;
  color: #1470FF;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.contact-item:hover i {
  color: #4A90FF;
  transform: scale(1.1);
}

.contact-item h4 {
  font-size: 20px;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.contact-item p {
  font-size: 16px;
  color: #666;
  margin: 0;
  position: relative;
  z-index: 2;
}

/* 立即开始 */
.cta-section {
  background: linear-gradient(135deg, #0a0a0a 0%, #1470FF 50%, #4A90FF 100%);
  color: white;
  padding: 20px 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255,255,255,0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255,255,255,0.06) 0%, transparent 50%),
    linear-gradient(135deg, transparent 0%, rgba(255,255,255,0.03) 50%, transparent 100%);
  animation: backgroundShift 8s ease-in-out infinite alternate;
  z-index: 1;
}

@keyframes backgroundShift {
  0% {
    transform: translateX(-10px) translateY(-10px) scale(1);
  }
  100% {
    transform: translateX(10px) translateY(10px) scale(1.02);
  }
}

.cta-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 70%, rgba(20, 112, 255, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 70% 30%, rgba(74, 144, 255, 0.2) 0%, transparent 50%),
              radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
  z-index: 1;
}



.cta-content {
  position: relative;
  z-index: 2;
  animation: ctaGlow 3s ease-in-out infinite alternate;
}

@keyframes ctaGlow {
  0% {
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
  }
  100% {
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.5), 0 0 40px rgba(20, 112, 255, 0.3);
  }
}

.cta-content h2 {
  font-size: 36px;
  margin-bottom: 20px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3), 0 0 20px rgba(255, 255, 255, 0.2);
  animation: titlePulse 2s ease-in-out infinite;
}

@keyframes titlePulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.cta-content p {
  font-size: 18px;
  margin-bottom: 40px;
  opacity: 0.95;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-buttons .am-btn {
  padding: 15px 35px;
  font-size: 16px;
  border-radius: 30px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  font-weight: 500;
  min-width: 160px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.2), 0 0 20px rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  animation: buttonPulse 2s ease-in-out infinite;
}

@keyframes buttonPulse {
  0%, 100% {
    box-shadow: 0 4px 15px rgba(0,0,0,0.2), 0 0 20px rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow: 0 6px 20px rgba(0,0,0,0.3), 0 0 30px rgba(255, 255, 255, 0.2);
  }
}

.am-btn-primary {
  background: linear-gradient(45deg, #fff 0%, #f0f8ff 100%);
  color: #1470FF;
  border: 2px solid #fff;
}

.am-btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(20, 112, 255, 0.2), transparent);
  transition: left 0.5s;
}

.am-btn-primary:hover::before {
  left: 100%;
}

.am-btn-primary:hover {
  background: linear-gradient(45deg, #f8f9fa 0%, #e6f2ff 100%);
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 10px 30px rgba(0,0,0,0.25), 0 0 40px rgba(20, 112, 255, 0.3);
  color: #1470FF;
  animation: none;
}

.am-btn-secondary:hover {
  background: #fff;
  color: #1470FF;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-title {
    font-size: 32px;
  }

  .banner-subtitle {
    font-size: 16px;
  }

  .about-banner {
    padding: 80px 0 60px;
  }

  .about-section,
  .our-mission,
  .our-team,
  .contact-section,
  .cta-section {
    padding: 60px 0;
  }

  .section--title {
    font-size: 28px;
  }

  .section--description {
    font-size: 16px;
  }

  .stat-number {
    font-size: 36px;
  }

  .our_mission--item {
    margin-bottom: 40px;
  }

  .cta-content h2 {
    font-size: 28px;
  }

  .cta-content p {
    font-size: 16px;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-buttons .am-btn {
    width: 200px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 10px;
  }

  .banner-title {
    font-size: 28px;
  }

  .section--title {
    font-size: 24px;
  }

  .stat-number {
    font-size: 32px;
  }

  .our_mission--item,
  .contact-item {
    padding: 20px 10px;
  }
}
</style>